# MaterialSearch 开发环境配置
# 开发环境特定的配置覆盖

# 服务器配置
server:
  debug: true
  host: "127.0.0.1"
  port: 8000
  reload: true

# 数据库配置
database:
  host: "${PROD_DB_HOST:localhost}"
  name: "materialsearch"
  user: "${PROD_DB_USER:materialsearch}"
  password: "${PROD_DB_PASSWORD}"
  pool_size: 50

# 模型配置
model:
  batch_size: 2  # 开发环境使用较小批量
  cache_dir: "./models"

# 扫描配置
scan:
  assets_paths:
    - "./test_data/images"
    - "./test_data/videos"
  auto_save_interval: 10  # 更频繁的保存

# 日志配置
logging:
  level: "DEBUG"
  console_enabled: true
  file_enabled: false  # 开发环境不写文件日志

# 任务配置
task:
  background_workers: 2
  max_concurrent_uploads: 5
